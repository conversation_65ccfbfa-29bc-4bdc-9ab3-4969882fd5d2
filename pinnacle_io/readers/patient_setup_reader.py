"""
Reader for Pinnacle plan.PatientSetup files.
"""

import os
from typing import Any
from pinnacle_io.models import PatientSetup
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class PatientSetupReader:
    """
    Reader for Pinnacle plan.PatientSetup files.
    """

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> PatientSetup:
        """
        Read a Pinnacle plan.PatientSetup file and return the PatientSetup models.

        Args:
            plan_path: Path to the Pinnacle plan.PatientSetup file or directory
            file_service: File service object with open_file method

        Returns:
            PatientSetup model populated with data from the file
        
        Usage:
            patient_setup = PatientSetupReader.read("/path/to/Patient_0/Plan_0/plan.PatientSetup")
            patient_setup = PatientSetupReader.read("/path/to/Patient_0/Plan_0")
            patient_setup = PatientSetupReader.read("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Extract the file path and name
        if plan_path.lower().endswith("plan.patientsetup"):
            file_path, file_name = os.path.split(plan_path)
        else:
            file_path, file_name = plan_path, "plan.PatientSetup"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"plan.PatientSetup file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return PatientSetupReader.parse(f.readlines())
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"plan.PatientSetup file not found: {full_path}")
        
        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return PatientSetupReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> PatientSetup:
        """
        Parse a Pinnacle plan.PatientSetup content string and create a PatientSetup model.

        Args:
            content_lines: Pinnacle plan.PatientSetup content lines

        Returns:
            PatientSetup model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient_setup = PatientSetup(**data)
        return patient_setup
