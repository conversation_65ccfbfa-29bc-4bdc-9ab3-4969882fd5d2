"""
Reader for Pinnacle plan.Trial files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Trial
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader


class TrialReader:
    """
    Reader for Pinnacle plan.Trial files.
    """

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> List[Trial]:
        """
        Read a Pinnacle plan.Trial file and return the Trial models.
        The patient setup information is also processed and attached to the Trial models.

        Args:
            plan_path: Path to the Pinnacle plan.Trial file or directory
            file_service: File service object with open_file method

        Returns:
            List of Trial models populated with data from the file
        
        Usage:
            trials = TrialReader.read("/path/to/Patient_0/Plan_0/plan.Trial")
            trials = TrialReader.read("/path/to/Patient_0/Plan_0")
            trials = TrialReader.read("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Extract the file path and name
        if plan_path.endswith("plan.Trial"):
            file_path, file_name = os.path.split(plan_path)
        else:
            file_path, file_name = plan_path, "plan.Trial"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"plan.Trial file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                trials = TrialReader.parse(f.readlines())

            # Read patient setup using the same file service
            try:
                patient_position = PatientSetupReader.read(file_path, file_service=file_service)
                for trial in trials:
                    trial._patient_position = patient_position
            except FileNotFoundError:
                # Patient setup is optional
                pass
            return trials
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"plan.Trial file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            trials = TrialReader.parse(f.readlines())

        # Try to read patient setup from the same directory
        try:
            patient_position = PatientSetupReader.read(file_path)
            for trial in trials:
                trial._patient_position = patient_position
        except FileNotFoundError:
            # Patient setup is optional
            pass
        return trials

    @staticmethod
    def parse(content_lines: list[str]) -> List[Trial]:
        """
        Parse a Pinnacle plan.Trial file content and create Trial models.
        The patient setup information is NOT processed by this method.

        Args:
            content_lines: Lines from a Pinnacle plan.Trial file

        Returns:
            List of Trial models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        trials = [Trial(**trial, trial_id=i) for i, trial in enumerate(data.get("TrialList", []))]
        return trials
