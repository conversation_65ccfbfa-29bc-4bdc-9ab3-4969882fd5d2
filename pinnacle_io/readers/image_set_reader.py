"""
Reader for Pinnacle ImageSet files.
"""

import os
import re
import numpy as np
from typing import Any
from pinnacle_io.models import ImageSet
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.image_info_reader import ImageInfoReader


class ImageSetReader:
    """
    Reader for Pinnacle ImageSet files.

    This class provides methods for reading ImageSet files and creating models from the data in the files.
    """

    @staticmethod
    def read(image_path: str, image_index: int | None = None, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet file and create an ImageSet model.

        This method determines whether the provided path is for a binary image file (.img)
        or a header file (.header), and delegates to the appropriate reader. The ImageInfoList
        is also read from the corresponding ImageInfo file.

        Args:
            image_path: Path to ImageSet_# file (either .img or .header extension is required)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Example:
            >>> image_set = ImageSetReader.read("/path/to/ImageSet_0.img")
            >>> image_set = ImageSetReader.read("/path/to/ImageSet_0.header")
            >>> image_set = ImageSetReader.read("/path/to/Patient", image_index=0)
        """
        if image_path.endswith(".img"):
            return ImageSetReader.read_image_set(image_path, image_index, file_service)

        # If not specified as an img, assume it's a header file
        return ImageSetReader.read_header(image_path, image_index, file_service)

    @staticmethod
    def read_header(image_header_path: str, image_index: int | None = None, file_service: Any = None) -> ImageSet:
        """
        Read a Pinnacle ImageSet header file and create an ImageSet model.

        This method reads the header file for an ImageSet, parses its contents, and loads
        the associated ImageInfoList from the corresponding ImageInfo file.

        Args:
            image_header_path: Path to ImageSet_# file (the .header extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Raises:
            FileNotFoundError: If the header file does not exist
            ValueError: If the image index is not specified and cannot be inferred

        Example:
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0/ImageSet_0.header")
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0/ImageSet_0")
            >>> image_set = ImageSetReader.read_header("/path/to/Patient_0", image_index=0)
        """
        # Extract the file path and name
        if image_header_path.endswith(".header"):
            file_path, file_name = os.path.split(image_header_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_header_path)):
            file_path, file_name = os.path.split(image_header_path)
            file_name += ".header"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_header_path or in the image_index argument"
                )
            file_path, file_name = image_header_path, f"ImageSet_{image_index}.header"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"ImageSet header file not found: {file_path}/{file_name}")

            with file_service.open_file(file_path, file_name, "r") as f:
                image_set = ImageSetReader.parse(f.readlines())

            # Read corresponding ImageInfo file
            info_filename = file_name.replace(".header", ".ImageInfo")
            image_set.image_info_list = ImageInfoReader.read(
                os.path.join(file_path, info_filename), file_service=file_service
            )
            return image_set

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet header file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            image_set = ImageSetReader.parse(f.readlines())

        # Read corresponding ImageInfo file
        info_file_path = full_path.replace(".header", ".ImageInfo")
        image_set.image_info_list = ImageInfoReader.read(info_file_path)
        return image_set

    @staticmethod
    def read_image_set(
        image_set_path: str,
        image_index: int | None = None,
        image_header: ImageSet | None = None,
        file_service: Any = None,
    ) -> ImageSet:
        """
        Read a Pinnacle ImageSet binary file and create an ImageSet model.

        This method reads the binary pixel data from the specified .img file, using header
        information to determine the image dimensions. If the header is not provided, it is
        loaded automatically. The pixel data is loaded as a numpy array.

        Args:
            image_set_path: Path to ImageSet_# file (the .img extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img
            image_header: Optional ImageSet model containing header information
            file_service: File service object with open_file method

        Returns:
            ImageSet model populated with data from the file

        Raises:
            FileNotFoundError: If the image set file does not exist
            ValueError: If the image index is not specified and cannot be inferred

        Example:
            >>> image_set = ImageSetReader.read_image_set("/path/to/ImageSet_0.img")
            >>> image_set = ImageSetReader.read_image_set("/path/to/ImageSet_0")
            >>> image_set = ImageSetReader.read_image_set("/path/to/Patient", image_index=0)
        """
        # Extract the file path and name
        if image_set_path.endswith(".img"):
            file_path, file_name = os.path.split(image_set_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_set_path)):
            file_path, file_name = os.path.split(image_set_path)
            file_name += ".img"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_header_path or in the image_index argument"
                )
            file_path, file_name = image_set_path, f"ImageSet_{image_index}.img"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"ImageSet file not found: {file_path}/{file_name}")

            if image_header is None:
                header_filename = file_name.replace(".img", ".header")
                header_path = os.path.join(file_path, header_filename)
                image_set = ImageSetReader.read_header(header_path, file_service=file_service)
            else:
                image_set = image_header

            z_dim = image_set.z_dim if image_set.z_dim is not None else 1
            y_dim = image_set.y_dim if image_set.y_dim is not None else 1
            x_dim = image_set.x_dim if image_set.x_dim is not None else 1

            with file_service.open_file(file_path, file_name, "rb") as f:
                binary_data = f.read()
                pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)

            image_set.pixel_data = pixel_data
            return image_set

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet file not found: {full_path}")

        if image_header is None:
            header_file_path = full_path.replace(".img", ".header")
            image_set = ImageSetReader.read_header(header_file_path)
        else:
            image_set = image_header

        z_dim = image_set.z_dim if image_set.z_dim is not None else 1
        y_dim = image_set.y_dim if image_set.y_dim is not None else 1
        x_dim = image_set.x_dim if image_set.x_dim is not None else 1

        with open(full_path, "rb") as f:
            binary_data = f.read()
            pixel_data = np.frombuffer(binary_data, dtype=np.uint16).reshape(z_dim, y_dim, x_dim)

        image_set.pixel_data = pixel_data
        return image_set

    @staticmethod
    def read_image_slice(
        image_set_path: str, image_header: ImageSet, slice_index: int, image_index: int | None = None
    ) -> np.ndarray:
        """
        Extract a single 2D slice from a 3D image set by reading only the necessary bytes.

        This method enables fast thumbnail generation without loading the entire dataset
        by calculating the byte offset and size for the specific slice and reading only
        those bytes from the file.

        Args:
            image_set_path: Path to ImageSet_# file (the .img extension is optional)
            image_header: An existing ImageSet object containing metadata about the image
                         dimensions and structure
            slice_index: Zero-based index of the slice to extract from the 3D image set
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.img

        Returns:
            2D numpy array representing the extracted slice with shape (y_dim, x_dim)

        Raises:
            FileNotFoundError: If the image set file does not exist
            ValueError: If slice_index is out of bounds, slice_index is not an integer, or image dimensions are invalid

        Example:
            >>> header = ImageSetReader.read_header("ImageSet_0.header")
            >>> slice_data = ImageSetReader.read_image_slice("ImageSet_0.img", header, 50)
            >>> slice_data = ImageSetReader.read_image_slice("ImageSet_0", header, 50)
            >>> slice_data = ImageSetReader.read_image_slice("/path/to/Patient", header, 50, image_index=0)
            >>> print(f"Slice shape: {slice_data.shape}")
        """
        # Extract the file path and name
        if image_set_path.endswith(".img"):
            file_path, file_name = os.path.split(image_set_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_set_path)):
            file_path, file_name = os.path.split(image_set_path)
            file_name += ".img"
        else:
            if image_index is None:
                raise ValueError(
                    "The image index must either be specified in the image_set_path or in the image_index argument"
                )
            file_path, file_name = image_set_path, f"ImageSet_{image_index}.img"

        # Validate inputs
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet file not found: {full_path}")

        # Get dimensions from header, defaulting to 1 if None (following existing pattern)
        z_dim = image_header.z_dim if image_header.z_dim is not None else 1
        y_dim = image_header.y_dim if image_header.y_dim is not None else 1
        x_dim = image_header.x_dim if image_header.x_dim is not None else 1

        # Validate slice index
        if slice_index < 0 or slice_index >= z_dim:
            raise ValueError(f"Slice index {slice_index} is out of bounds for image with {z_dim} slices")
        if slice_index != int(slice_index):
            raise ValueError(f"Slice index {slice_index} is not an integer")

        # Calculate byte offset and size for the specific slice
        # Each pixel is 2 bytes (np.uint16), slice size is y_dim * x_dim pixels
        bytes_per_pixel = 2  # np.uint16
        slice_size_bytes = y_dim * x_dim * bytes_per_pixel
        slice_offset_bytes = int(slice_index) * slice_size_bytes

        # Read only the bytes for the requested slice
        with open(full_path, "rb") as f:
            f.seek(slice_offset_bytes)
            slice_bytes = f.read(slice_size_bytes)

            # Verify we read the expected amount of data
            if len(slice_bytes) != slice_size_bytes:
                raise ValueError(f"Expected to read {slice_size_bytes} bytes but got {len(slice_bytes)} bytes")

            # Convert bytes to numpy array and reshape to 2D slice
            slice_data = np.frombuffer(slice_bytes, dtype=np.uint16).reshape(y_dim, x_dim)

        return slice_data

    @staticmethod
    def parse(content_lines: list[str]) -> ImageSet:
        """
        Parse a Pinnacle ImageSet header content string and create an ImageSet model.

        The ImageInfoList is not parsed.

        Args:
            content_lines: Pinnacle ImageSet header content lines

        Returns:
            ImageSet model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_set = ImageSet(**data)
        return image_set
