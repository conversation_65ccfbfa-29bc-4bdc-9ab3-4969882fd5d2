"""
Reader for Pinnacle plan.Points files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Point
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class PointReader:
    """
    Reader for Pinnacle plan.Points files.
    """

    @staticmethod
    def read(plan_path: str, file_service: Any = None) -> List[Point]:
        """
        Read a Pinnacle plan.Points file and create a list of Point models.

        Args:
            plan_path: Path to the patient's plan directory or plan.Points file
            file_service: File service object with open_file method

        Returns:
            List of Point models populated with data from the file
        
        Usage:
            points = PointReader.read("/path/to/Patient_0/Plan_0/plan.Points")
            points = PointReader.read("/path/to/Patient_0/Plan_0")
            points = PointReader.read("/path/to/Patient_0/Plan_0", file_service=file_service)
        """
        # Extract the file path and name
        if plan_path.endswith("plan.Points"):
            file_path, file_name = os.path.split(plan_path)
        else:
            file_path, file_name = plan_path, "plan.Points"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"plan.Points file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return PointReader.parse(f.readlines())
        
        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"plan.Points file not found: {full_path}")
        
        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return PointReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> List[Point]:
        """
        Parse a Pinnacle plan.Points file content and create a list of Point models.

        Args:
            content_lines: Lines from a Pinnacle plan.Points file

        Returns:
            List of Point models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        return [Point(**point) for point in data["PoiList"]]
