"""
Reader for Pinnacle ImageInfo files (extracted from ImageSetReader).
"""

import os
import re
from typing import Any
from pinnacle_io.models import ImageInfo
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class ImageInfoReader:
    """
    Reader for Pinnacle ImageSet_#.ImageInfo files.
    """

    @staticmethod
    def read(image_info_path: str, image_index: int | None = None, file_service: Any = None) -> list[ImageInfo]:
        """
        Read a Pinnacle ImageSet info file and create a list of ImageInfo models.

        Args:
            image_info_path: Path to ImageSet_# file or directory (the .ImageInfo extension is optional)
            image_index: Optional index of the image to read, e.g., 0 for ImageSet_0.ImageInfo
            file_service: File service object with open_file method

        Returns:
            List of ImageInfo models populated with data from the file

        Usage:
            image_set = ImageSetReader.read_image_set("/path/to/Patient_0/ImageSet_0.ImageInfo")
            image_set = ImageSetReader.read_image_set("/path/to/Patient_0/ImageSet_0")
            image_set = ImageSetReader.read_image_set("/path/to/Patient_0", image_index=0)
        """
        # Extract the file path and name
        if image_info_path.endswith(".ImageInfo"):
            file_path, file_name = os.path.split(image_info_path)
        elif re.match(r".*ImageSet_\d+$", os.path.basename(image_info_path)):
            file_path, file_name = os.path.split(image_info_path)
            file_name += ".ImageInfo"
        else:
            if image_index is None:
                raise ValueError("The image index must either be specified in the image_header_path or in the image_index argument")
            file_path, file_name = image_info_path, f"ImageSet_{image_index}.ImageInfo"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"ImageSet info file not found: {file_path}/{file_name}")

            with file_service.open_file(file_path, file_name, "r") as f:
                return ImageInfoReader.parse(f.readlines())

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"ImageSet info file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            return ImageInfoReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> list[ImageInfo]:
        """
        Parse a Pinnacle ImageSet info content string and create a list of ImageInfo models.

        Args:
            content_lines: Pinnacle ImageSet info content lines

        Returns:
            List of ImageInfo models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        image_info_list = [ImageInfo(**image_info) for image_info in data.get("ImageInfoList", {})]
        return image_info_list
