"""
Reader for Pinnacle Plan files.
"""

import os
from typing import List, Any
from pinnacle_io.models import Plan, Patient
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader
from pinnacle_io.readers.patient_setup_reader import PatientSetupReader


class PlanReader:
    """
    Reader for Pinnacle Plan files.
    """

    @staticmethod
    def read(patient_path: str, file_service: Any = None) -> List[Plan]:
        """
        Read a Pinnacle Patient file and return the Plan models.
        The patient setup information is also processed and attached to the Plan models.

        Args:
            patient_path: Path to the Pinnacle Patient file or directory
            file_service: File service object with open_file method

        Returns:
            List of Plan models populated with data from the file
        
        Usage:
            plans = PlanReader.read("/path/to/Patient_0/Patient")
            plans = PlanReader.read("/path/to/Patient_0", file_service=file_service)
        """
        # Extract the file path and name
        if patient_path.endswith("Patient"):
            file_path, file_name = os.path.split(patient_path)
        else:
            file_path, file_name = patient_path, "Patient"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"Patient file not found: {file_path}/{file_name}")

            with file_service.open_file(file_path, file_name, "r") as f:
                plans = PlanReader.parse(f.readlines())

            # Try to read patient setup for each plan using file service
            for i, plan in enumerate(plans):
                try:
                    plan_dir = os.path.join(file_path, f"Plan_{i}")
                    patient_setup = PatientSetupReader.read(plan_dir, file_service=file_service)
                    plan.set_patient_position(patient_setup)  # type: ignore
                except FileNotFoundError:
                    # Patient setup is optional
                    pass
            return plans

        # Default to file system operations
        full_path = os.path.join(file_path, file_name)
        if not os.path.exists(full_path):
            raise FileNotFoundError(f"Patient file not found: {full_path}")

        with open(full_path, "r", encoding="latin1", errors="ignore") as f:
            plans = PlanReader.parse(f.readlines())

        # Try to read patient setup for each plan
        for i, plan in enumerate(plans):
            try:
                plan_dir = os.path.join(file_path, f"Plan_{i}")
                patient_setup = PatientSetupReader.read(plan_dir)
                plan.set_patient_position(patient_setup)  # type: ignore
            except FileNotFoundError:
                # Patient setup is optional
                pass

        return plans

    @staticmethod
    def parse(content_lines: list[str]) -> List[Plan]:
        """
        Parse a Pinnacle Patient file content and create Plan models.
        The patient setup information is NOT processed by this method.

        Args:
            content_lines: Lines from a Pinnacle Patient file

        Returns:
            List of Plan models populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient = Patient(**data)
        return patient.plan_list
