"""
Reader for Pinnacle Patient files.
"""

import os
from typing import Any
from pinnacle_io.models import Patient
from pinnacle_io.readers.pinnacle_file_reader import PinnacleFileReader


class PatientReader:
    """
    Reader for Pinnacle Patient files.
    """

    @staticmethod
    def read(patient_path: str, file_service: Any = None) -> Patient:
        """
        Read a Pinnacle Patient file and create a Patient model.

        Args:
            patient_path: Path to the Patient file or directory
            file_service: File service object with open_file method

        Returns:
            Patient model populated with data from the file
        
        Usage:
            patient = PatientReader.read("/path/to/Patient_0/Patient")
            patient = PatientReader.read("/path/to/Patient_0")
            patient = PatientReader.read("/path/to/Patient_0", file_service=file_service)
        """
        # Extract the file path and name
        if patient_path.endswith("Patient"):
            file_path, file_name = os.path.split(patient_path)
        else:
            file_path, file_name = patient_path, "Patient"

        # Use the file service if provided
        if file_service is not None:
            if not file_service.exists(file_path, file_name):
                raise FileNotFoundError(f"Patient file not found: {file_path}/{file_name}")
            
            with file_service.open_file(file_path, file_name, "r") as f:
                return PatientReader.parse(f.readlines())
        
        # Default to file system operations
        file_path = os.path.join(file_path, file_name)
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Patient file not found: {file_path}")

        with open(file_path, "r", encoding="latin1", errors="ignore") as f:
            return PatientReader.parse(f.readlines())

    @staticmethod
    def parse(content_lines: list[str]) -> Patient:
        """
        Parse a Pinnacle Patient content string and create a Patient model.

        Args:
            content_lines: Pinnacle Patient content lines

        Returns:
            Patient model populated with data from the content
        """
        data = PinnacleFileReader.parse(content_lines)
        patient = Patient(**data)
        return patient
