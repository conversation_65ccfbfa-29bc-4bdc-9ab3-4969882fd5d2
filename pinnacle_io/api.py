"""
Unified API for reading and writing Pinnacle data.

This module provides the main entry points for the pinnacle_io library,
offering a simple interface for accessing Pinnacle treatment planning data
from various sources including directories, tar files, and zip files.
"""

import os
from pathlib import Path
from typing import Any, TYPE_CHECKING

if TYPE_CHECKING:
    import numpy as np

from .services.base_reader_service import BaseReaderService
from .services.file_reader import FileReader
from .services.tar_file_reader import TarFileReader
from .services.zip_file_reader import ZipFileReader
from .models import (
    Institution,
    Patient,
    ImageSet,
    ImageInfo,
    Trial,
    ROI,
    Point,
    PatientSetup,
    Machine,
    Dose,
)


def read(path: str | Path, target_file: str | None = None) -> Any:
    """
    Read Pinnacle data from various sources.

    .. deprecated:: 0.1.0
        Use :class:`PinnacleReader` instead. This function will be removed in a future version.

    Automatically detects the source type (directory, tar file, or zip file)
    and returns the appropriate data object. If target_file is specified,
    reads only that specific file type.

    Args:
        path: Path to the data source (directory, tar file, or zip file)
        target_file: Optional full path to the specific file to read (e.g., "Institution", "/path/to/Patient_#/Patient")

    Returns:
        The appropriate data object based on the file type

    Raises:
        FileNotFoundError: If the specified path does not exist
        ValueError: If the file format is not supported

    Examples:
        # DEPRECATED: Use PinnacleReader instead
        # institution = read("/path/to/pinnacle/data", "Institution")

        # PREFERRED: Use PinnacleReader
        reader = PinnacleReader("/path/to/pinnacle/data")
        institution = reader.get_institution()
    """
    path = Path(path)

    if not path.exists():
        raise FileNotFoundError(f"Path not found: {path}")

    # Determine the appropriate reader service
    if path.is_dir():
        reader_service = FileReader(str(path))
    elif path.suffix.lower() in [".tar", ".gz", ".tgz"] or path.name.endswith(".tar.gz"):
        reader_service = TarFileReader(str(path))
    elif path.suffix.lower() == ".zip":
        reader_service = ZipFileReader(str(path))
    else:
        raise ValueError(f"Unsupported file format: {path.suffix}")

    # If target_file is specified, read that specific file
    if target_file:
        return _read_specific_file(reader_service, target_file)

    # Otherwise, try to read Institution as the default
    try:
        return reader_service.get_institution()
    except Exception:
        # If Institution doesn't exist, return the reader service
        # so users can call specific methods
        return reader_service


def write(path: str | Path, obj: Any) -> None:
    """
    Write Pinnacle data to a directory.

    .. deprecated:: 0.1.0
        This function is deprecated and will be removed in a future version.
        Writing functionality is not yet implemented.

    Currently only supports writing to directories. Writing to tar or zip
    files is not yet implemented.

    Args:
        path: Path to the target directory
        obj: The data object to write

    Raises:
        NotImplementedError: For tar/zip file writing (not yet supported)
        ValueError: If the path is not a directory

    Examples:
        # DEPRECATED: Writing functionality not yet implemented
        # write("/path/to/output", institution)
    """
    path = Path(path)

    if not path.is_dir():
        if path.suffix.lower() in [".tar", ".gz", ".tgz", ".zip"] or path.name.endswith(".tar.gz"):
            raise NotImplementedError("Writing to tar/zip files is not yet supported")
        else:
            raise ValueError(f"Path must be a directory for writing: {path}")

    # For now, we'll raise NotImplementedError as writing functionality
    # needs to be implemented with the appropriate writer classes
    raise NotImplementedError("Writing functionality is not yet implemented")


def _read_specific_file(reader_service: BaseReaderService, target_file: str) -> Any:
    """
    Read a specific file type using the appropriate reader method.

    Args:
        reader_service: The reader service to use
        target_file: The specific file to read

    Returns:
        The appropriate data object

    Raises:
        ValueError: If the target_file is not recognized
    """
    # Handle different file types based on their paths and extract appropriate directory paths
    if not target_file or target_file.endswith("Institution"):
        # Institution file is at root level
        return reader_service.get_institution()
    elif target_file.endswith("Patient"):
        # Patient file path: Institution_#/Mount_0/Patient_#/Patient
        return reader_service.get_patient(target_file)
    elif "ImageSet_" in target_file:
        if target_file.endswith(".header") or target_file.endswith(".img"):
            # ImageSet files: Institution_#/Mount_0/Patient_#/ImageSet_#.header
            return reader_service.get_image_set(target_file)
        elif target_file.endswith(".ImageInfo"):
            # ImageInfo files: Institution_#/Mount_0/Patient_#/ImageSet_#.ImageInfo
            return reader_service.get_image_info(target_file)
    elif target_file.endswith("plan.Trial"):
        # Plan files: Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial
        return reader_service.get_trials(target_file)
    elif target_file.endswith("plan.roi"):
        # Plan files: Institution_#/Mount_0/Patient_#/Plan_#/plan.roi
        return reader_service.get_rois(target_file)
    elif target_file.endswith("plan.Points"):
        # Plan files: Institution_#/Mount_0/Patient_#/Plan_#/plan.Points
        return reader_service.get_points(target_file)
    elif target_file.endswith("plan.PatientSetup"):
        # Plan files: Institution_#/Mount_0/Patient_#/Plan_#/plan.PatientSetup
        return reader_service.get_patient_setup(target_file)
    elif target_file.endswith("plan.Pinnacle.Machines"):
        # Plan files: Institution_#/Mount_0/Patient_#/Plan_#/plan.Pinnacle.Machines
        return reader_service.get_machines(target_file)
    elif "plan.Trial.binary" in target_file:
        # Dose files: Institution_#/Mount_0/Patient_#/Plan_#/plan.Trial.binary.###
        return reader_service.get_dose(os.path.dirname(target_file))

    raise ValueError(f"Unknown target file type: {target_file}")


class PinnacleReader:
    """
    Recommended class for reading Pinnacle data.

    This class provides the preferred interface for reading Pinnacle treatment planning data
    from various sources including directories, tar files, and zip files. It manages the
    appropriate reader service internally based on the source type.

    Args:
        path: Path to the data source (directory, tar file, or zip file)

    Examples:
        # Initialize a Pinnacle reader
        reader = PinnacleReader("/path/to/pinnacle/data")
        institution = reader.get_institution()

        # Load patients from institution
        for patient_lite in institution.patient_lite_list:
            patient = reader.get_patient(patient_lite.patient_path)
            print(patient.last_and_first_name)
    """

    def __init__(self, path: str | Path):
        """Initialize the PinnacleReader with the given path."""
        self.path = Path(path)
        self._reader_service: BaseReaderService
        self._initialize_reader_service()

    def _initialize_reader_service(self) -> None:
        """Initialize the appropriate reader service based on path type."""
        if not self.path.exists():
            raise FileNotFoundError(f"Path not found: {self.path}")

        if self.path.is_dir():
            self._reader_service = FileReader(str(self.path))
        elif self.path.suffix.lower() in [".tar", ".gz", ".tgz"] or self.path.name.endswith(".tar.gz"):
            self._reader_service = TarFileReader(str(self.path))
        elif self.path.suffix.lower() == ".zip":
            self._reader_service = ZipFileReader(str(self.path))
        else:
            raise ValueError(f"Unsupported file format: {self.path.suffix}")

    def get_institution(self) -> Institution:
        """
        Retrieve the Institution object from the Pinnacle data source.

        The Institution object contains facility-level information including the institution name,
        location details, and a list of patient references. This is typically the root object
        in a Pinnacle data hierarchy.

        Returns:
            Institution: The institution object containing facility information and patient references.

        Raises:
            FileNotFoundError: If the Institution file is not found in the data source.
            ValueError: If the Institution file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> institution = reader.get_institution()
            >>> print(f"Institution: {institution.name}")
            >>> print(f"Number of patients: {len(institution.patient_lite_list)}")
        """
        return self._reader_service.get_institution()

    def get_patient(self, patient_path: str | None = None) -> Patient:
        """
        Retrieve a Patient object from the Pinnacle data source.

        The Patient object contains comprehensive patient information including demographics,
        medical record details, associated image sets, and treatment plans. Patient data
        is typically organized within the Institution hierarchy.

        Args:
            patient_path: Optional path to the specific patient directory or Patient file.
                         If None, attempts to auto-discover the first available patient.
                         Can be a relative path like "Institution_1/Mount_0/Patient_1"
                         or just "Patient_1" for simplified access.

        Returns:
            Patient: The patient object containing demographics, image sets, and plans.

        Raises:
            FileNotFoundError: If the specified patient path or Patient file is not found.
            ValueError: If the Patient file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Auto-discover first patient
            >>> patient = reader.get_patient()
            >>> print(f"Patient: {patient.last_and_first_name}")
            >>>
            >>> # Load specific patient by path
            >>> patient = reader.get_patient("Institution_1/Mount_0/Patient_1")
            >>> print(f"MRN: {patient.medical_record_number}")
        """
        if patient_path is None:
            patient_path = ""  # Default to root for auto-discovery
        return self._reader_service.get_patient(patient_path)

    def get_image_set(self, image_path: str = "", index: int = 0) -> ImageSet:
        """
        Retrieve an ImageSet object containing medical imaging data.

        The ImageSet object represents a complete medical image series (CT, MR, PET, etc.)
        including pixel data, DICOM metadata, spatial coordinates, and associated image
        information. This is the primary container for volumetric imaging data used in
        treatment planning.

        Args:
            image_path: Path to the ImageSet file or directory containing the image data.
                       Can be a specific file like "ImageSet_0.header" or "ImageSet_0.img",
                       or a directory path for auto-discovery. Defaults to current directory.
            index: Index of the ImageSet to retrieve when multiple image sets are available.
                  Defaults to 0 (first image set).

        Returns:
            ImageSet: The image set object containing pixel data, metadata, and spatial information.
                     The pixel_data attribute contains a numpy array with shape (z, y, x) where
                     z is the number of slices, y is the anterior-posterior dimension, and
                     x is the left-right dimension.

        Raises:
            FileNotFoundError: If the ImageSet header or image files are not found.
            ValueError: If the image index is out of range or file format is invalid.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Load first image set
            >>> image_set = reader.get_image_set()
            >>> print(f"Modality: {image_set.modality}")
            >>> print(f"Dimensions: {image_set.get_image_dimensions()}")
            >>>
            >>> # Load specific image set
            >>> ct_images = reader.get_image_set("Patient_1", index=0)
            >>> slice_data = ct_images.get_slice_data(50)  # Get middle slice
        """
        return self._reader_service.get_image_set(image_path, index)

    def get_image_slice(
        self, image_path: str = "", slice_index: int = 0, image_header: "ImageSet | None" = None, image_index: int = 0
    ) -> np.ndarray:
        """
        Retrieve a single 2D slice from a 3D image set without loading the entire dataset.

        This method provides efficient access to individual image slices by reading only the
        necessary bytes from the image file, enabling fast thumbnail generation and slice
        preview without the memory overhead of loading complete volumetric datasets.

        Args:
            image_path: Path to the ImageSet file or directory containing the image data.
                       Can be a specific file like "ImageSet_0.img" or "ImageSet_0",
                       or a directory path for auto-discovery. Defaults to current directory.
            slice_index: Zero-based index of the slice to extract from the 3D image set.
                        Must be within the range [0, z_dim-1] where z_dim is the number
                        of slices in the image set. Defaults to 0 (first slice).
            image_header: Optional ImageSet object containing metadata. If None, header will be loaded automatically.
            image_index: Index of the ImageSet to use when multiple image sets are available.
                        Defaults to 0 (first image set).

        Returns:
            numpy.ndarray: A 2D numpy array representing the extracted slice with shape (y_dim, x_dim)
                          where y_dim is the anterior-posterior dimension and x_dim is the left-right
                          dimension. The array contains pixel values as uint16 data type.

        Raises:
            FileNotFoundError: If the ImageSet header or image files are not found.
            ValueError: If the slice_index is out of bounds, image_index is invalid,
                       or file format is corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> # Get first slice from first image set (automatic header loading)
            >>> slice_data = reader.get_image_slice()
            >>> print(f"Slice shape: {slice_data.shape}")
            >>>
            >>> # Get middle slice from specific image set
            >>> slice_data = reader.get_image_slice("Patient_1", slice_index=50, image_index=0)
            >>> print(f"Pixel value range: {slice_data.min()} - {slice_data.max()}")
            >>>
            >>> # Use with pre-loaded header for efficiency
            >>> header = reader.get_image_set("Patient_1", 0)
            >>> slice_data = reader.get_image_slice("Patient_1", slice_index=50, image_header=header, image_index=0)
            >>>
            >>> # Use for thumbnail generation
            >>> import matplotlib.pyplot as plt
            >>> plt.imshow(slice_data, cmap='gray')
            >>> plt.title(f"Slice {slice_index}")
        """
        return self._reader_service.get_image_slice(image_path, slice_index, image_header, image_index)

    def get_image_info(self, image_info_path: str = "", image_index: int = 0) -> list[ImageInfo]:
        """
        Retrieve a list of ImageInfo objects containing per-slice metadata.

        ImageInfo objects contain detailed DICOM metadata for individual image slices,
        including slice positions, acquisition parameters, timing information, and
        DICOM identifiers. This complements the ImageSet data with slice-specific details.

        Args:
            image_info_path: Path to the ImageInfo file or directory containing the image data.
                           Can be a specific file like "ImageSet_0.ImageInfo" or a directory
                           path for auto-discovery. Defaults to current directory.
            image_index: Index of the ImageSet whose ImageInfo should be retrieved.
                        Defaults to 0 (first image set).

        Returns:
            list[ImageInfo]: A list of ImageInfo objects, one for each slice in the image set.
                           Each object contains slice-specific DICOM metadata, positioning,
                           and acquisition parameters.

        Raises:
            FileNotFoundError: If the ImageInfo file is not found.
            ValueError: If the image index is out of range or file format is invalid.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> image_info_list = reader.get_image_info()
            >>> print(f"Number of slices: {len(image_info_list)}")
            >>>
            >>> # Access slice-specific information
            >>> first_slice = image_info_list[0]
            >>> print(f"Table position: {first_slice.table_position} mm")
            >>> print(f"Slice thickness: {first_slice.slice_thickness} mm")
            >>> print(f"DICOM file: {first_slice.dicom_file_name}")
        """
        return self._reader_service.get_image_info(image_info_path, image_index)

    def get_trials(self, plan_path: str = "") -> list[Trial]:
        """
        Retrieve a list of Trial objects from a treatment plan.

        Trial objects represent individual treatment delivery scenarios within a treatment plan,
        containing beam configurations, dose calculation parameters, optimization settings,
        and delivery instructions. Each trial represents a complete treatment approach.

        Args:
            plan_path: Path to the plan directory or plan.Trial file containing trial data.
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.

        Returns:
            list[Trial]: A list of Trial objects representing different treatment scenarios.
                        Each trial contains beam parameters, dose calculation settings,
                        optimization constraints, and delivery parameters.

        Raises:
            FileNotFoundError: If the plan.Trial file is not found in the specified path.
            ValueError: If the Trial file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> trials = reader.get_trials("Patient_1/Plan_0")
            >>> print(f"Number of trials: {len(trials)}")
            >>>
            >>> # Access trial information
            >>> primary_trial = trials[0]
            >>> print(f"Trial name: {primary_trial.name}")
            >>> print(f"Number of beams: {len(primary_trial.beam_list)}")
            >>> print(f"Prescription dose: {primary_trial.dose_per_fraction} cGy")
        """
        return self._reader_service.get_trials(plan_path)

    def get_rois(self, plan_path: str = "") -> list[ROI]:
        """
        Retrieve a list of Region of Interest (ROI) objects from a treatment plan.

        ROI objects represent anatomical structures, target volumes, and organs at risk
        that have been contoured for treatment planning. Each ROI contains geometric
        contour data, volumetric properties, and clinical classification information.

        Args:
            plan_path: Path to the plan directory or plan.roi file containing ROI data.
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.

        Returns:
            list[ROI]: A list of ROI objects representing contoured anatomical structures.
                      Each ROI contains contour data, volume calculations, statistical
                      properties, and clinical classification information.

        Raises:
            FileNotFoundError: If the plan.roi file is not found in the specified path.
            ValueError: If the ROI file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> rois = reader.get_rois("Patient_1/Plan_0")
            >>> print(f"Number of ROIs: {len(rois)}")
            >>>
            >>> # Access ROI information
            >>> for roi in rois:
            ...     print(f"ROI: {roi.name} ({roi.type})")
            ...     print(f"Volume: {roi.volume:.2f} cm³")
            ...     print(f"Color: RGB({roi.color_red}, {roi.color_green}, {roi.color_blue})")
        """
        return self._reader_service.get_rois(plan_path)

    def get_points(self, plan_path: str = "") -> list[Point]:
        """
        Retrieve a list of Point objects from a treatment plan.

        Point objects represent discrete spatial locations within the treatment planning
        coordinate system, including reference points, fiducial markers, isocenter
        locations, and dose calculation points. These points serve various clinical
        and technical purposes in treatment planning.

        Args:
            plan_path: Path to the plan directory or plan.Points file containing point data.
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.

        Returns:
            list[Point]: A list of Point objects representing spatial reference locations.
                        Each point contains 3D coordinates, clinical classification,
                        and associated metadata for treatment planning reference.

        Raises:
            FileNotFoundError: If the plan.Points file is not found in the specified path.
            ValueError: If the Points file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> points = reader.get_points("Patient_1/Plan_0")
            >>> print(f"Number of points: {len(points)}")
            >>>
            >>> # Access point information
            >>> for point in points:
            ...     print(f"Point: {point.name}")
            ...     print(f"Coordinates: ({point.x:.1f}, {point.y:.1f}, {point.z:.1f}) mm")
            ...     print(f"Type: {point.type}")
        """
        return self._reader_service.get_points(plan_path)

    def get_patient_setup(self, plan_path: str = "") -> PatientSetup:
        """
        Retrieve the PatientSetup object from a treatment plan.

        The PatientSetup object contains patient positioning and immobilization information
        used for treatment delivery, including patient orientation, support structures,
        positioning aids, and setup verification parameters.

        Args:
            plan_path: Path to the plan directory or plan.PatientSetup file containing setup data.
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.

        Returns:
            PatientSetup: The patient setup object containing positioning and immobilization data.
                         Includes patient orientation, support equipment, positioning aids,
                         and verification parameters for accurate treatment delivery.

        Raises:
            FileNotFoundError: If the plan.PatientSetup file is not found in the specified path.
            ValueError: If the PatientSetup file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> setup = reader.get_patient_setup("Patient_1/Plan_0")
            >>> print(f"Patient position: {setup.patient_position}")
            >>> print(f"Head support: {setup.head_support}")
            >>> print(f"Setup technique: {setup.setup_technique}")
            >>>
            >>> # Access positioning tolerances
            >>> print(f"Position tolerance: ±{setup.position_tolerance} mm")
        """
        return self._reader_service.get_patient_setup(plan_path)

    def get_machines(self, plan_path: str = "") -> list[Machine]:
        """
        Retrieve a list of Machine objects from a treatment plan.

        Machine objects represent linear accelerators and their configuration parameters
        used for treatment delivery, including beam energies, field sizes, dose rates,
        multi-leaf collimator (MLC) specifications, and safety parameters.

        Args:
            plan_path: Path to the plan directory or plan.Pinnacle.Machines file containing machine data.
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.

        Returns:
            list[Machine]: A list of Machine objects representing treatment delivery equipment.
                          Each machine contains energy specifications, field parameters,
                          MLC configuration, dose rate settings, and safety constraints.

        Raises:
            FileNotFoundError: If the plan.Pinnacle.Machines file is not found in the specified path.
            ValueError: If the Machines file format is invalid or corrupted.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> machines = reader.get_machines("Patient_1/Plan_0")
            >>> print(f"Number of machines: {len(machines)}")
            >>>
            >>> # Access machine information
            >>> linac = machines[0]
            >>> print(f"Machine: {linac.name}")
            >>> print(f"Manufacturer: {linac.manufacturer}")
            >>> print(f"Available energies: {len(linac.photon_energy_list)} MV")
            >>> print(f"Max field size: {linac.max_field_x} x {linac.max_field_y} cm²")
        """
        return self._reader_service.get_machines(plan_path)

    def get_dose(self, plan_path: str = "", trial: Trial | None = None) -> Dose:
        """
        Retrieve the Dose object from a treatment plan.

        The Dose object contains the three-dimensional dose distribution calculated for
        a specific trial, including dose grid data, statistical parameters, and spatial
        coordinate information. This represents the calculated radiation dose throughout
        the patient volume.

        Args:
            plan_path: Path to the plan directory containing dose files (plan.Trial.binary.###).
                      Can be a specific path like "Patient_1/Plan_0" or relative path.
                      Defaults to current directory for auto-discovery.
            trial: Optional Trial object to specify which trial's dose to retrieve.
                  If None, attempts to load the dose for the first available trial.

        Returns:
            Dose: The dose object containing 3D dose distribution data, statistical parameters,
                 and spatial coordinate information. The dose_data attribute contains a numpy
                 array with the dose values in cGy for each voxel in the calculation grid.

        Raises:
            FileNotFoundError: If the dose binary files are not found in the specified path.
            ValueError: If the dose files are corrupted or the trial is not found.

        Example:
            >>> reader = PinnacleReader("/path/to/pinnacle/data")
            >>> dose = reader.get_dose("Patient_1/Plan_0")
            >>> print(f"Dose grid dimensions: {dose.x_dim} x {dose.y_dim} x {dose.z_dim}")
            >>> print(f"Dose grid spacing: {dose.x_pixdim:.2f} mm")
            >>> print(f"Maximum dose: {dose.maximum_dose:.1f} cGy")
            >>>
            >>> # Access dose for a specific trial
            >>> trials = reader.get_trials("Patient_1/Plan_0")
            >>> dose = reader.get_dose("Patient_1/Plan_0", trial=trials[0])
        """
        return self._reader_service.get_dose(plan_path, trial)
