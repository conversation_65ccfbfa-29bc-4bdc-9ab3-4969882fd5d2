from abc import ABC, abstractmethod
import numpy as np
from typing import Any, List, IO
from pinnacle_io.models import (
    Dose,
    ImageInfo,
    ImageSet,
    Institution,
    Machine,
    Patient,
    PatientSetup,
    Point,
    ROI,
    Trial,
)
from pinnacle_io.readers import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    ImageInfoReader,
    ImageSetReader,
    InstitutionReader,
    MachineReader,
    PatientReader,
    PatientSetupReader,
    PointReader,
    ROIReader,
    TrialReader,
)


class BaseReaderService(ABC):
    """
    Abstract base class for unified Pinnacle IO services.

    Provides a uniform interface for accessing Pinnacle data objects from various sources
    including directories, tar files, zip files, and potentially in-memory archives.

    This service abstraction enables the same code to work with different data sources
    without needing to know the underlying storage mechanism. All concrete implementations
    must provide file access methods (open_file, exists) and inherit the standardized
    get_* methods for reading specific Pinnacle data types.

    Supported Data Types:
        - Institution: Root-level institution information
        - Patient: Patient demographic and medical information
        - ImageSet: Medical imaging data sets
        - ImageInfo: Metadata about imaging data
        - Trial: Treatment trial definitions
        - ROI: Regions of interest (treatment volumes)
        - Point: Treatment planning point data
        - PatientSetup: Patient positioning information
        - Machine: Treatment delivery machine configurations
        - Dose: Dose distribution data from treatment calculations

    Usage:
        reader_service = FileReader("/path/to/data")
        institution = reader_service.get_institution()

        reader_service = TarFileReader("/path/to/data.tar.gz")
        patient = reader_service.get_patient()
    """

    @abstractmethod
    def open_file(self, filepath: str, filename: str | None = None, mode: str = "r") -> IO[Any]:
        """
        Open a file-like object for the given filepath and filename from the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
            mode: File mode (e.g., 'r', 'rb').
        Returns:
            A file-like object.
        Raises:
            FileNotFoundError: If the specified file does not exist in the source.
        """
        pass

    @abstractmethod
    def exists(self, filepath: str, filename: str | None = None) -> bool:
        """
        Check if a file exists in the underlying source.
        Args:
            filepath: The directory path or full file path within the source.
            filename: Optional filename to append to filepath. If None, filepath is treated as full path.
        Returns:
            True if the file exists, False otherwise.
        """
        pass

    def get_institution(self) -> Institution:
        """
        Retrieve an Institution object from the source.

        Returns:
            Institution object using the file service abstraction.
        """
        return InstitutionReader.read(institution_path="", file_service=self)

    def get_patient(self, patient_path: str) -> Patient:
        """
        Retrieve a Patient object from the source.

        Args:
            patient_path: Path to Patient file or directory within the source.

        Returns:
            Patient object using the file service abstraction.
        """
        return PatientReader.read(patient_path, file_service=self)

    def get_image_header(self, image_header_path: str, index: int = 0) -> ImageSet:
        """
        Retrieve an ImageSet object from the source without loading the pixel data.

        Args:
            image_header_path: Path to ImageSet file or directory within the source.
            index: Index of the ImageSet to retrieve (default: 0).

        Returns:
            ImageSet object (minus the pixel data) using the file service abstraction.
        """
        return ImageSetReader.read_header(image_header_path, image_index=index, file_service=self)

    def get_image_set(self, image_path: str, index: int = 0) -> ImageSet:
        """
        Retrieve an ImageSet object from the source.

        Args:
            image_path: Path to ImageSet file or directory within the source.
            index: Index of the ImageSet to retrieve (default: 0).

        Returns:
            ImageSet object using the file service abstraction.
        """
        return ImageSetReader.read_image_set(image_path, image_index=index, file_service=self)

    def get_image_slice(
        self, image_path: str, slice_index: int, image_header: "ImageSet | None" = None, image_index: int = 0
    ) -> np.ndarray:
        """
        Retrieve a single 2D slice from an ImageSet without loading the entire dataset.

        Args:
            image_path: Path to ImageSet file or directory within the source.
            slice_index: Index of the slice to retrieve.
            image_header: Optional ImageSet object containing metadata. If None, header will be loaded automatically.
            image_index: Index of the ImageSet to retrieve (default: 0).

        Returns:
            2D numpy array representing the extracted slice using the file service abstraction.
        """
        return ImageSetReader.read_image_slice(image_path, slice_index, image_header, image_index, file_service=self)

    def get_image_info(self, image_info_path: str, image_index: int = 0) -> list[ImageInfo]:
        """
        Retrieve a list of ImageInfo objects from the source.

        Args:
            image_info_path: Path to ImageInfo file or directory within the source.
            image_index: Index of the ImageInfo to retrieve (default: 0).

        Returns:
            List of ImageInfo objects using the file service abstraction.
        """
        return ImageInfoReader.read(image_info_path, image_index=image_index, file_service=self)

    def get_trials(self, plan_path: str) -> List[Trial]:
        """
        Retrieve a list of Trial objects from the source.

        Args:
            plan_path: Path to plan directory or plan.Trial file within the source.

        Returns:
            List of Trial objects using the file service abstraction.
        """
        return TrialReader.read(plan_path, file_service=self)

    def get_rois(self, plan_path: str) -> List[ROI]:
        """
        Retrieve a list of ROI objects from the source.

        Args:
            plan_path: Path to plan directory or plan.roi file within the source.

        Returns:
            List of ROI objects using the file service abstraction.
        """
        return ROIReader.read(plan_path, file_service=self)

    def get_points(self, plan_path: str) -> List[Point]:
        """
        Retrieve a list of Point objects from the source.

        Args:
            plan_path: Path to plan directory or plan.Points file within the source.

        Returns:
            List of Point objects using the file service abstraction.
        """
        return PointReader.read(plan_path, file_service=self)

    def get_patient_setup(self, plan_path: str) -> PatientSetup:
        """
        Retrieve a PatientSetup object from the source.

        Args:
            plan_path: Path to plan directory or plan.PatientSetup file within the source.

        Returns:
            PatientSetup object using the file service abstraction.
        """
        return PatientSetupReader.read(plan_path, file_service=self)

    def get_machines(self, plan_path: str) -> List[Machine]:
        """
        Retrieve a list of Machine objects from the source.

        Args:
            plan_path: Path to plan directory or plan.Pinnacle.Machines file within the source.

        Returns:
            List of Machine objects using the file service abstraction.
        """
        return MachineReader.read(plan_path, file_service=self)

    def get_dose(self, plan_path: str, trial: Trial | None = None) -> Dose:
        """
        Retrieve a Dose object from the source.

        Args:
            plan_path: Relative path to the plan directory within the file service
            trial: Optionally specify a Trial object for which to retrieve the dose.

        Returns:
            Dose object using the file service abstraction.
        """
        return DoseReader.read(plan_path, trial, file_service=self)
