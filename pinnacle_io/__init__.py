"""
pinnacle_io: Python package for reading and writing Pinnacle data.

This package provides a unified interface for accessing Pinnacle treatment planning data
from various sources including directories, tar files, and zip files.
"""

# Main API functions
from .api import read, write, PinnacleReader

# Reader services
from .services.base_reader_service import BaseReaderService
from .services.file_reader import FileReader
from .services.tar_file_reader import TarFileReader
from .services.zip_file_reader import ZipFileReader

# Data models
from .models.institution import Institution
from .models.patient import Patient
from .models.image_set import ImageSet
from .models.image_info import ImageInfo
from .models.trial import Trial
from .models.roi import ROI
from .models.point import Point
from .models.patient_setup import PatientSetup
from .models.machine import Machine
from .models.dose import Dose
from .models.plan import Plan
from .models.beam import Beam
from .models.control_point import ControlPoint

# Reader classes
from .readers.institution_reader import InstitutionReader
from .readers.patient_reader import PatientReader
from .readers.image_set_reader import ImageSetReader
from .readers.image_info_reader import ImageInfoReader
from .readers.trial_reader import TrialReader
from .readers.roi_reader import <PERSON><PERSON><PERSON>eader
from .readers.point_reader import PointReader
from .readers.patient_setup_reader import Pat<PERSON><PERSON>etupReader
from .readers.machine_reader import MachineReader
from .readers.dose_reader import DoseReader
from .readers.plan_reader import PlanReader

__version__ = "0.1.0"

__all__ = [
    # Reader services
    "BaseReaderService",
    "FileReader",
    "TarFileReader",
    "ZipFileReader",
    # Data models
    "Institution",
    "Patient",
    "ImageSet",
    "ImageInfo",
    "Trial",
    "ROI",
    "Point",
    "PatientSetup",
    "Machine",
    "Dose",
    "Plan",
    "Beam",
    "ControlPoint",
    # Reader classes
    "InstitutionReader",
    "PatientReader",
    "ImageSetReader",
    "ImageInfoReader",
    "TrialReader",
    "ROIReader",
    "PointReader",
    "PatientSetupReader",
    "MachineReader",
    "DoseReader",
    "PlanReader",
]
