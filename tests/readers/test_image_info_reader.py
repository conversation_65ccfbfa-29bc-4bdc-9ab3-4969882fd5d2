"""
Tests for the ImageInfoReader class.
"""

from pathlib import Path
from pinnacle_io.models import ImageInfo
from pinnacle_io.readers.image_info_reader import ImageInfoReader
from pinnacle_io.services.file_reader import <PERSON><PERSON>eader


def test_read_image_info_from_file():
    """Tests reading ImageInfo from a .ImageInfo file using direct path (backward compatibility)."""
    image_info_list = ImageInfoReader.read(image_info_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/ImageSet_0.ImageInfo"))
    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    # Check known values from the test data
    assert image_info.table_position == -8.75
    assert image_info.couch_pos == 8.75
    assert image_info.slice_number == 1
    assert image_info.series_uid == "1.2.840.113619.2.55.3.**********.111.**********.1"
    assert image_info.study_instance_uid == "1.2.840.113619.2.55.3.**********.111.**********.1"
    assert image_info.frame_uid == "1.2.840.113619.2.55.3.**********.111.**********.2"
    assert image_info.class_uid == "1.2.840.10008.5.1.4.1.1.2"
    assert image_info.instance_uid == "1.2.840.113619.2.55.3.**********.111.**********.3.1"
    assert image_info.dicom_file_name == "CT_1.2.840.113619.2.55.3.**********.111.**********.3.1.dcm"
    assert image_info.acquisition_time == "120000"
    assert image_info.image_time == "120000"


def test_read_image_info_from_file_with_service():
    """Tests reading ImageInfo from a .ImageInfo file using file service."""
    test_data_root = Path(__file__).parent.parent / "test_data/01"
    file_service = FileReader(str(test_data_root))
    image_info_path = "Institution_1/Mount_0/Patient_1"
    image_info_list = ImageInfoReader.read(image_info_path, image_index=0, file_service=file_service)

    assert len(image_info_list) > 0
    image_info = image_info_list[0]
    assert isinstance(image_info, ImageInfo)
    # Check known values from the test data
    assert image_info.table_position == -8.75
