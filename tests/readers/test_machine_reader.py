from pathlib import Path
from pinnacle_io.models import Machine, MachineEnergy, PhysicsData, OutputFactor, ElectronApplicator, TolTable, MLCLeafPair
from pinnacle_io.readers.machine_reader import MachineReader
from pinnacle_io.services.file_reader import FileReader


def test_read_machine_file():
    """Tests reading a valid Machine file using direct path (backward compatibility)."""
    machine_list = MachineReader.read(plan_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"))

    assert isinstance(machine_list, list)
    assert len(machine_list) == 1
    machine = machine_list[0]
    assert isinstance(machine, Machine)
    assert machine.name == "Clinac iX"
    assert machine.machine_type == "Varian Clinac-2100"
    assert machine.commissioned_for_photons == 1
    assert machine.commissioned_for_electrons == 1
    assert machine.sad == 100.0

    # Test angle configurations
    assert machine.couch_angle.minimum_angle == 265.0
    assert machine.couch_angle.maximum_angle == 95.0
    assert machine.gantry_angle.minimum_angle == 180.0
    assert machine.gantry_angle.maximum_angle == 179.9
    assert machine.collimator_angle.minimum_angle == 195.0
    assert machine.collimator_angle.maximum_angle == 165.0

    # Test ConfigRV
    assert machine.config_rv.enabled == 1
    assert machine.config_rv.left_jaw == "X1"
    assert machine.config_rv.right_jaw == "X2"

    # Test MultiLeaf
    assert machine.multi_leaf is not None
    assert machine.multi_leaf.left_bank_name == "B"
    assert machine.multi_leaf.right_bank_name == "A"

    # Test LeafPairList and MLCLeafPairs
    assert machine.multi_leaf.leaf_pair_list is not None
    assert len(machine.multi_leaf.leaf_pair_list) > 0
    assert isinstance(machine.multi_leaf.leaf_pair_list[0], MLCLeafPair)

    # Test Electron Applicators
    assert len(machine.electron_applicator_list) > 0
    assert isinstance(machine.electron_applicator_list[0], ElectronApplicator)

    # Test Tolerance Tables
    assert len(machine.tolerance_table_list) > 0
    assert isinstance(machine.tolerance_table_list[0], TolTable)

    # Test Photon Energies
    assert len(machine.photon_energy_list) > 0
    energy = machine.photon_energy_list[0]
    assert isinstance(energy, MachineEnergy)
    assert energy.physics_data is not None
    assert isinstance(energy.physics_data, PhysicsData)
    assert energy.physics_data.output_factor is not None
    assert isinstance(energy.physics_data.output_factor, OutputFactor)


def test_read_machine_file_with_service():
    """Tests reading a valid Machine file using file service."""
    plan_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    file_service = FileReader(str(plan_path))
    machine_list = MachineReader.read(plan_path="", file_service=file_service)

    assert isinstance(machine_list, list)
    assert len(machine_list) == 1
    machine = machine_list[0]
    assert isinstance(machine, Machine)
    assert machine.name == "Clinac iX"


def test_read_electron_applicator_data():
    """Tests reading ElectronApplicator data from a Machine file."""
    machine_list = MachineReader.read(plan_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"))

    machine = machine_list[0]
    assert len(machine.electron_applicator_list) > 0

    # Get the first applicator for detailed testing
    applicator = machine.electron_applicator_list[0]
    assert isinstance(applicator, ElectronApplicator)
    assert applicator.name is not None
    assert isinstance(applicator.width, (float, type(None)))
    assert isinstance(applicator.length, (float, type(None)))
    assert isinstance(applicator.manufacturer_code, (str, type(None)))

    # Test cutout properties if they exist
    if applicator.cutout_material is not None:
        assert isinstance(applicator.cutout_material, str)
    if applicator.cutout_mass_density is not None:
        assert isinstance(applicator.cutout_mass_density, float)
    if applicator.cutout_is_divergent is not None:
        assert isinstance(applicator.cutout_is_divergent, int)
    if applicator.cutout_is_rectangular is not None:
        assert isinstance(applicator.cutout_is_rectangular, int)
