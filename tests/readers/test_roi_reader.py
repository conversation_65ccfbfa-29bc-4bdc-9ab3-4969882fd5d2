"""
Tests for the ROIReader (extracted from test_roi.py).
"""

from pathlib import Path
import pytest
import numpy as np

from pinnacle_io.readers.roi_reader import ROIReader
from pinnacle_io.models import RO<PERSON>, Curve
from pinnacle_io.services.file_reader import <PERSON><PERSON>eader


def test_read_roi_file():
    """Tests reading a valid ROI file using direct path (backward compatibility)."""
    rois = ROIReader.read(plan_path=str(Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"))

    assert isinstance(rois, list)
    assert all(isinstance(roi, ROI) for roi in rois)
    assert len(rois) > 0

    # Test the first ROI
    roi = rois[0]
    assert roi.name == "bb"
    assert roi.volume_name == "LAST^FIRST^M"
    assert roi.stats_volume_name == "LAST^FIRST^M"
    assert roi.flags == 135168
    assert roi.roi_interpreted_type == "ORGAN"
    assert roi.color == "red"
    assert roi.box_size == 5.0
    assert roi.line_2d_width == 2
    assert roi.line_3d_width == 1
    assert roi.paint_brush_radius == 0.4
    assert roi.paint_allow_curve_closing is True
    assert roi.curve_min_area == 0.1
    assert roi.curve_overlap_min == 88.0
    assert roi.lower_threshold == 800.0
    assert roi.upper_threshold == 4096.0
    assert roi.radius == 0.0
    assert roi.density == 0.0
    assert roi.density_units == "g/cm^3"
    assert roi.override_data is True
    assert roi.override_order == 1
    assert roi.override_material is False
    assert roi.material is None
    assert roi.invert_density_loading is False
    assert roi.volume == 0.953669
    assert roi.pixel_min == 0.0
    assert roi.pixel_max == 4071.0
    assert roi.pixel_mean == 750.617
    assert roi.pixel_std == 1072.4
    assert roi.bev_drr_outline is False
    assert roi.display_on_other_vols is True
    assert roi.is_linked is False

    # Test curves in the first ROI
    assert len(roi.curve_list) > 0
    curve = roi.curve_list[0]
    assert curve.flags == 131092
    assert curve.block_size == 32
    assert curve.num_points == 25
    assert curve.curve_number == 0
    assert curve.point_count == 25
    assert len(curve.points) == 25
    assert len(curve.get_curve_data()) == 75  # 25 points * 3 coordinates


def test_read_roi_file_with_service():
    """Tests reading a valid ROI file using file service."""
    plan_path = Path(__file__).parent.parent / "test_data/01/Institution_1/Mount_0/Patient_1/Plan_0"
    file_service = FileReader(str(plan_path))
    rois = ROIReader.read(plan_path="", file_service=file_service)

    assert isinstance(rois, list)
    assert all(isinstance(roi, ROI) for roi in rois)
    assert len(rois) > 0

    # Test the first ROI
    roi = rois[0]
    assert roi.name == "bb"
